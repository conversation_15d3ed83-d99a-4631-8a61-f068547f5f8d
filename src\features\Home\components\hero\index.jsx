import bannerImg1 from "../../../../assets/images/factoryImg2.jpg";
import { useState, useEffect, useCallback } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function HeroSection() {
  const slides = [
    {
      image: bannerImg1,
      title: "High-Quality Air Pumps",
      subtitle: "Direct from China",
      description:
        "Discover durable, efficient, and affordable air pump solutions — perfect for industrial applications. Shipped from China with guaranteed performance.",
      buttonText: "Explore Products",
    },
    {
      image: bannerImg1,
      title: "Industrial Grade",
      subtitle: "Pumping Solutions",
      description:
        "Professional air pumps designed for heavy-duty industrial use. Built to last with superior engineering and reliable performance.",
      buttonText: "View Catalog",
    },
    {
      image: bannerImg1,
      title: "Wholesale Pricing",
      subtitle: "Direct Manufacturing",
      description:
        "Get the best prices on premium air pumps. Direct from manufacturer to your business with bulk pricing and fast shipping.",
      buttonText: "Get Quote",
    },
  ];

  const [current, setCurrent] = useState(1); // Start at 1 (first real slide)
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Create extended slides array with duplicates for infinite effect
  const extendedSlides = [slides[slides.length - 1], ...slides, slides[0]];

  const handleTransitionEnd = () => {
    setIsTransitioning(false);
    if (current === 0) {
      // If we're at the duplicate last slide, jump to real last slide
      setCurrent(slides.length);
    } else if (current === extendedSlides.length - 1) {
      // If we're at the duplicate first slide, jump to real first slide
      setCurrent(1);
    }
  };

  const prevSlide = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrent((prev) => prev - 1);
  };

  const nextSlide = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrent((prev) => prev + 1);
  }, [isTransitioning]);

  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 10000);
    return () => clearInterval(interval);
  }, [nextSlide]);

  const goToSlide = (index) => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrent(index + 1); // +1 because of the duplicate slide at the beginning
  };

  return (
    <div className="relative h-[calc(100vh-var(--navbar-height))] w-full overflow-hidden ">
      {/* Slides Container - Images and Text move together */}
      <div
        className={`flex h-full ${
          isTransitioning ? "transition-transform duration-700 ease-in-out" : ""
        }`}
        style={{ transform: `translateX(-${current * 100}%)` }}
        onTransitionEnd={handleTransitionEnd}
      >
        {extendedSlides.map((slide, index) => (
          <div key={index} className="relative w-full h-full flex-shrink-0">
            {/* Background Image */}
            <img
              src={slide.image}
              alt={`Slide ${index}`}
              className="w-full h-full object-cover -"
            />

            {/* Overlay for this slide */}
            <div className="absolute inset-0 bg-black/60"></div>

            {/* Text Content for this slide - Added padding to avoid navigation buttons */}
            <div className="absolute inset-0 flex flex-col justify-center items-center text-white text-center px-16 sm:px-20 md:px-24 lg:px-4">
              <h1 className="text-lg sm:text-xl md:text-2xl lg:text-4xl xl:text-5xl font-bold mb-2 sm:mb-3 md:mb-4 lg:mb-5 leading-tight">
                {slide.title} <br /> {slide.subtitle}
              </h1>
              <p className="text-xs sm:text-sm md:text-base lg:text-lg mb-3 sm:mb-4 md:mb-5 lg:mb-6 max-w-xs sm:max-w-sm md:max-w-md lg:max-w-xl leading-relaxed">
                {slide.description}
              </p>
              <button className="bg-[var(--primary)] hover:bg-[var(--primary)]/90 text-white font-medium rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg text-xs sm:text-sm md:text-base px-3 sm:px-4 md:px-5 lg:px-6 py-2 sm:py-2.5 md:py-3">
                {slide.buttonText}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Arrows - Reduced size on smaller screens */}
      <div className="absolute left-2 sm:left-3 md:left-4 lg:left-6 xl:left-8 top-1/2 -translate-y-1/2 z-40">
        <button
          className="group flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 xl:w-16 xl:h-16 
                     bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 
                     rounded-full transition-all duration-300 hover:scale-110 hover:shadow-lg"
          onClick={prevSlide}
        >
          <ChevronLeft
            className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 xl:w-7 xl:h-7 text-white 
                                 group-hover:text-white transition-colors duration-300"
          />
        </button>
      </div>

      <div className="absolute right-2 sm:right-3 md:right-4 lg:right-6 xl:right-8 top-1/2 -translate-y-1/2 z-40">
        <button
          className="group flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 xl:w-16 xl:h-16 
                     bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 
                     rounded-full transition-all duration-300 hover:scale-110 hover:shadow-lg"
          onClick={nextSlide}
        >
          <ChevronRight
            className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 xl:w-7 xl:h-7 text-white 
                                  group-hover:text-white transition-colors duration-300"
          />
        </button>
      </div>

      {/* Dots - Reduced size on smaller screens */}
      <div className="absolute bottom-4 sm:bottom-6 md:bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2 sm:gap-3 z-40">
        {slides.map((_, i) => (
          <button
            key={i}
            onClick={() => goToSlide(i)}
            className={`w-2 h-1  md:w-3 md:h-1.5 lg:w-4 lg:h-2 rounded-full transition-all duration-300 hover:scale-125 ${
              i === (current - 1 + slides.length) % slides.length
                ? "bg-white shadow-lg"
                : "bg-white/40 hover:bg-white/60"
            }`}
          />
        ))}
      </div>
    </div>
  );
}
