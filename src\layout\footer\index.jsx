import { Link } from "react-router-dom";
import {
  Facebook,
  Instagram,
  Phone,
  Mail,
  MapPin,
  Printer,
  MessageCircle,
} from "lucide-react";

export default function Footer() {
  return (
    <footer className="bg-[#1c1a30] text-white py-20 px-6 md:px-20">
      <div className="max-w-screen-xl mx-auto flex flex-col lg:flex-row justify-between gap-16">
        {/* Logo and Summary */}
        <div className="flex-1 min-w-[220px]">
          <h2 className="text-3xl font-extrabold text-[var(--primary)]">
            Pyramid Power
          </h2>
          <p className="text-sm text-gray-300 mt-4 leading-relaxed max-w-xs">
            Pyramid Power Hafez is a leading provider of industrial air
            compressors and solutions, committed to delivering reliable
            performance and long-term efficiency.
          </p>
        </div>

        {/* Quick Links */}
        <div className="flex-1 min-w-[160px]">
          <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
          <ul className="space-y-3 text-sm text-gray-300">
            <li>
              <Link to="/" className="hover:text-[var(--primary)] transition">
                Home
              </Link>
            </li>
            <li>
              <Link
                to="/products"
                className="hover:text-[var(--primary)] transition"
              >
                Products
              </Link>
            </li>
            <li>
              <Link
                to="/applications"
                className="hover:text-[var(--primary)] transition"
              >
                Applications
              </Link>
            </li>
            <li>
              <Link
                to="/about"
                className="hover:text-[var(--primary)] transition"
              >
                About
              </Link>
            </li>
            <li>
              <Link
                to="/contact"
                className="hover:text-[var(--primary)] transition"
              >
                Contact
              </Link>
            </li>
          </ul>
        </div>

        {/* Contact Information */}
        <div className="flex-1 min-w-[220px]">
          <h3 className="text-lg font-semibold mb-4">Contact Info</h3>
          <ul className="space-y-3 text-sm text-gray-300">
            <li className="flex items-start gap-3">
              <Phone size={16} className="text-[var(--primary)]" />{" "}
              <span>+971-123-456-789</span>
            </li>
            <li className="flex items-start gap-3">
              <Printer size={16} className="text-[var(--primary)]" />{" "}
              <span>+971-123-456-780</span>
            </li>
            <li className="flex items-start gap-3">
              <Mail size={16} className="text-[var(--primary)]" />{" "}
              <span><EMAIL></span>
            </li>
            <li className="flex items-start gap-3">
              <MapPin size={16} className="text-[var(--primary)]" />{" "}
              <span>Dubai Industrial Zone, UAE</span>
            </li>
          </ul>
        </div>

        {/* Social Media */}
        <div className="flex-1 min-w-[160px]">
          <h3 className="text-lg font-semibold mb-4">Connect With Us</h3>
          <div className="flex gap-4 items-center">
            <a
              href="#"
              className="text-[var(--primary)] hover:text-white transition "
            >
              <Facebook size={20} />
            </a>
            <a
              href="#"
              className="text-[var(--primary)] hover:text-white transition"
            >
              <Instagram size={20} />
            </a>
            <a
              href="https://wa.me/971123456789"
              target="_blank"
              rel="noopener noreferrer"
              className="text-[var(--primary)] hover:text-white transition"
            >
              <MessageCircle size={20} />
            </a>
          </div>
        </div>
      </div>

      <div className="mt-16 border-t border-gray-700 pt-6 text-center text-xs text-gray-500">
        © {new Date().getFullYear()} Pyramid Power Hafez. All rights reserved.
      </div>
    </footer>
  );
}
