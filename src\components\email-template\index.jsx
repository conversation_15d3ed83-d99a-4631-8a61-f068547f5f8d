import {
  Lightbulb,
  Users,
  PackageCheck,
  Globe,
  Phone,
  Mail,
  MapPin,
} from "lucide-react";
import img from "../../assets/images/workerImg.jpg";

export default function EmailTemplate() {
  return (
    <section className="relative z-0 font-sans">
      {/* Background Section with overlay */}
      <div
        className="pt-20 pb-80 px-6 md:px-20 text-white relative z-0"
        style={{
          backgroundImage: `linear-gradient(rgba(206, 22, 27, 0.85), rgba(206, 22, 27, 0.85)), url(${img})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="max-w-screen-xl mx-auto flex flex-col lg:flex-row lg:items-center lg:justify-between text-center lg:text-left gap-4">
          <h2 className="text-3xl md:text-4xl font-bold text-white">
            Custom Mining Equipment Solutions at Competitive Prices
          </h2>
          <p className="text-base md:text-lg max-w-2xl text-white lg:max-w-xl lg:ml-10">
            Pyramid Power delivers premium mining equipment and custom
            manufacturing services to clients worldwide...
          </p>
        </div>

        {/* Feature Cards */}
        <div className="max-w-screen-xl mx-auto mt-14 grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[
            {
              icon: (
                <Lightbulb className="text-[var(--primary)] bg-white rounded-full p-2 w-10 h-10" />
              ),
              title: "20 Years Of Experience",
              desc: "Over 20 years in the production of large mining equipment.",
            },
            {
              icon: (
                <Users className="text-[var(--primary)] bg-white rounded-full p-2 w-10 h-10" />
              ),
              title: "R&D & Production Team",
              desc: "Skilled design and production team for diverse materials.",
            },
            {
              icon: (
                <PackageCheck className="text-[var(--primary)] bg-white rounded-full p-2 w-10 h-10" />
              ),
              title: "Advanced Facilities",
              desc: "Large workshops and wide range of processing tools.",
            },
            {
              icon: (
                <Globe className="text-[var(--primary)] bg-white rounded-full p-2 w-10 h-10" />
              ),
              title: "Global Pricing Advantage",
              desc: "Competitive pricing with flexible payment methods.",
            },
          ].map((item, idx) => (
            <div
              key={idx}
              className="bg-white text-black rounded-lg p-6 shadow-md relative overflow-hidden transition-transform duration-300 transform hover:-translate-y-2 hover:ring-2 hover:ring-[var(--primary)] group"
            >
              <div>{item.icon}</div>
              <h4 className="font-semibold mt-6 mb-2 text-lg min-h-[48px]">
                {item.title}
              </h4>
              <p className="text-sm text-gray-600 leading-relaxed min-h-[60px]">
                {item.desc}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Inquiry Form + Details */}
      <div className="relative z-10 -mt-48">
        <div className="bg-white rounded-xl shadow-xl max-w-screen-xl mx-auto px-6 md:px-12 py-12 grid md:grid-cols-2 gap-10">
          {/* Left side: info text and contact icons */}
          <div>
            <h3 className="text-2xl font-bold text-[#1c1a30] mb-3">
              Send An Inquiry
            </h3>
            <p className="text-sm text-gray-600 mb-6">
              If you're looking for mining equipment such as crushers, dryers,
              ball mills, or kilns, reach out and our team will reply within 48
              hours.
            </p>
            <div className="border-t border-gray-300 pt-6 space-y-4">
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <Phone className="text-[var(--primary)]" size={18} />{" "}
                +971-123-456-789
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <Mail className="text-[var(--primary)]" size={18} />{" "}
                <EMAIL>
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <MapPin className="text-[var(--primary)]" size={18} /> Damascus
                , Syria
              </div>
            </div>
          </div>

          {/* Right side: form */}
          <form className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <input
              type="text"
              placeholder="Name"
              className="input input-bordered w-full"
            />
            <input
              type="text"
              placeholder="Phone"
              className="input input-bordered w-full"
            />
            <input
              type="email"
              placeholder="Email"
              className="input input-bordered w-full"
            />
            <input
              type="text"
              placeholder="Country"
              className="input input-bordered w-full"
            />
            <textarea
              placeholder="Please describe your needs..."
              rows={4}
              className="w-full textarea textarea-bordered sm:col-span-2"
            ></textarea>
            <button className=" btn bg-[var(--primary)] text-white hover:bg-[#b01419] sm:col-span-2">
              Contact Us
            </button>
          </form>
        </div>
      </div>

      {/* Stats */}
      <div className="mt-28 pb-20 px-6 md:px-20">
        <div className="max-w-screen-xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {[
            {
              number: "150+",
              label: "Global Projects In 2024",
              icon: <Globe size={30} className="text-[var(--primary)]" />,
            },
            {
              number: "500+",
              label: "Customers In China And Abroad",
              icon: (
                <PackageCheck size={30} className="text-[var(--primary)]" />
              ),
            },
            {
              number: "80+",
              label: "Professional Team Members",
              icon: <Users size={30} className="text-[var(--primary)]" />,
            },
            {
              number: "20+",
              label: "Years Of Experience",
              icon: <Lightbulb size={30} className="text-[var(--primary)]" />,
            },
          ].map((stat, idx) => (
            <div
              key={idx}
              className="flex items-center gap-4 p-4 bg-white rounded-lg shadow hover:shadow-md transition"
            >
              <div className="bg-[var(--primary)]/10 rounded-full w-14 h-14 flex items-center justify-center shrink-0">
                {stat.icon}
              </div>
              <div>
                <h4 className="text-2xl font-bold text-[#1c1a30]">
                  {stat.number}
                </h4>
                <p className="text-sm text-gray-600">{stat.label}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
