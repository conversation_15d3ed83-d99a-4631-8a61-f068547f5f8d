/* True zero-duplication infinite scroll - continuous loop */
.infinite-scroll-container {
  padding: 0 1rem;
  overflow: hidden;
  position: relative;
  /* Make container wide enough to show cards */
  width: 100%;
}

.infinite-scroll-track {
  display: flex;
  gap: 1rem;
  animation: continuousScroll 25s linear infinite;
  width: max-content;
}

.infinite-scroll-track:hover {
  animation-play-state: paused;
}

/* Continuous scroll that goes from right to left, then resets */
@keyframes continuousScroll {
  0% {
    /* Start from right side of container */
    transform: translateX(100vw);
  }
  100% {
    /* End when completely off the left side */
    transform: translateX(-100%);
  }
}

/* Responsive adjustments */
@media (min-width: 640px) {
  .infinite-scroll-track {
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  @keyframes continuousScroll {
    0% {
      /* Start from right side of container */
      transform: translateX(calc(100vw - 200px));
    }
    100% {
      /* End when completely off the left side */
      transform: translateX(-100%);
    }
  }
}
