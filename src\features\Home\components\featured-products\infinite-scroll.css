/* Simple CSS-only infinite scroll - much cleaner! */
.infinite-scroll-container {
  padding: 0 1rem;
  overflow: hidden;
}

.infinite-scroll-track {
  display: flex;
  gap: 1rem;
  animation: infiniteScroll 25s linear infinite;
  width: max-content;
}

.infinite-scroll-track:hover {
  animation-play-state: paused;
}

@keyframes infiniteScroll {
  0% {
    transform: translateX(0);
  }
  100% {
    /* Move by exactly half the width (since we duplicated the products) */
    transform: translateX(-50%);
  }
}

/* Responsive gap adjustments */
@media (min-width: 640px) {
  .infinite-scroll-track {
    gap: 1.5rem;
  }
}
